# Ollama Integration Guide

## Overview

This implementation provides a complete Ollama REST API integration for your "Sort the Court" game, using the Llama 3.2 3B model. It's designed as a drop-in replacement for the Player2 API with the same interface.

## Prerequisites

### 1. Install Ollama
Download and install Ollama from: https://ollama.ai/

### 2. Install Llama 3.2 3B Model
```bash
ollama pull llama3.2:3b
```

### 3. Start Ollama Server
```bash
ollama serve
```

The server will run on `http://127.0.0.1:11434` by default.

## Features

### ✅ **Complete API Compatibility**
- Same interface as Player2 API
- `create_chat_completion(character_history, additional_context)` function
- Drop-in replacement - just change `@onready var ai = Player2` to `@onready var ai = Ollama`

### ✅ **Ollama-Specific Optimizations**
- **Better Performance**: Ollama typically handles requests faster than cloud APIs
- **No Queue Limits**: Unlike Player2's queue limit of 2, Ollama can handle more concurrent requests
- **Local Processing**: No internet required, more reliable
- **Model Verification**: Automatically checks if llama3.2:3b is installed

### ✅ **Robust Error Handling**
- Model availability checking
- Specific error messages for common issues
- Automatic retry with exponential backoff
- Rate limiting to prevent overwhelming the local server

## Configuration

### Default Settings (Optimized for Llama 3.2 3B):
```gdscript
var model_name = "llama3.2:3b"
var ollama_url = "http://127.0.0.1:11434"
var min_request_interval = 1.0  # 1 second between requests
var max_concurrent_requests = 2  # Can handle more than Player2
```

### For Different Models:
```gdscript
# For larger models (slower but better quality)
var model_name = "llama3.2:7b"  # or "llama3.2:70b"
var min_request_interval = 2.0  # Longer delay for larger models

# For smaller models (faster but lower quality)  
var model_name = "llama3.2:1b"
var min_request_interval = 0.5  # Faster requests for smaller models
```

## API Usage

### Basic Usage (Same as Player2):
```gdscript
# Generate NPC name
var name = await Ollama.create_chat_completion([
    {"role": "user", "content": "Generate a medieval name"}
], "Generate only the name, nothing else.")

# Analyze decision consequences
var analysis = await Ollama.create_chat_completion([
    {"role": "user", "content": "Analyze this decision"}
], analysis_prompt)
```

### Ollama Request Format:
The implementation automatically converts to Ollama's chat format:
```json
{
  "model": "llama3.2:3b",
  "messages": [
    {"role": "system", "content": "system_context"},
    {"role": "user", "content": "user_message"}
  ],
  "stream": false,
  "options": {
    "temperature": 0.7,
    "top_p": 0.9,
    "max_tokens": 500
  }
}
```

## Testing

### 1. Run the Test Script
Add `ollama_test.gd` to your scene and run it to verify:
- Server connectivity
- Model availability  
- Basic chat functionality
- JSON response parsing
- Rate limiting
- Context/history handling

### 2. Expected Output
```
=== OLLAMA API TEST SCRIPT ===
Ollama server is ready with model: llama3.2:3b
Ollama test successful: Hello! I'm Claude, an AI assistant...
Name result: 'Aldric'
Chat result: 'Why did the knight go to the dentist? He had a cavity in his armor!'
...
=== ALL OLLAMA TESTS COMPLETED ===
```

## Troubleshooting

### Common Issues:

#### 1. "Ollama server is not available"
**Solution**: 
```bash
ollama serve
```
Make sure Ollama is running on port 11434.

#### 2. "Model llama3.2:3b not found"
**Solution**:
```bash
ollama pull llama3.2:3b
```
Wait for the model to download (about 2GB).

#### 3. "ERROR 500: Ollama internal server error"
**Causes**:
- Model not fully loaded
- Insufficient RAM (Llama 3.2 3B needs ~4GB)
- Corrupted model installation

**Solutions**:
```bash
ollama rm llama3.2:3b
ollama pull llama3.2:3b
```

#### 4. Slow Responses
**Optimizations**:
- Use smaller model: `llama3.2:1b`
- Reduce max_tokens: `"max_tokens": 200`
- Lower temperature: `"temperature": 0.3`

## Performance Comparison

### Ollama vs Player2:
| Feature | Player2 | Ollama |
|---------|---------|---------|
| **Queue Limit** | 2 requests | No limit |
| **Speed** | Network dependent | Local (faster) |
| **Reliability** | Internet required | Offline capable |
| **Cost** | Credits/subscription | Free after setup |
| **Model Control** | Fixed | Configurable |

## Game Integration

### Advantages for Your Game:
1. **No More 429 Errors**: No queue limits
2. **Faster Responses**: Local processing
3. **Better Reliability**: No network dependencies
4. **Cost Effective**: No ongoing API costs
5. **Privacy**: All processing stays local

### Game Performance:
- **NPC Generation**: ~1-2 seconds per NPC
- **Consequence Analysis**: ~2-3 seconds per decision
- **Concurrent Requests**: Can handle 2+ simultaneous requests
- **Memory Usage**: ~4GB RAM for the model

The game should now run much more smoothly with Ollama compared to the Player2 API!

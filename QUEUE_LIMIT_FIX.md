# AI Server Queue Limit Fix - 429 Error Resolution

## Root Cause Identified

The 429 error message revealed the real issue:
```
"Maximum requests queued in LLM. Current queue limit is 2. Upgrade your Patron tier to unlock more."
```

**The problem is NOT timing between requests, but the server's internal queue being full.**

## Key Understanding

- Your AI server has a **queue limit of 2 requests**
- When more than 2 requests are queued, you get a 429 error
- The server processes requests internally, so even with delays, multiple requests can queue up
- This is a **server-side limitation** based on your Patron tier

## Fixes Implemented

### 1. **Concurrent Request Limiting**
```gdscript
var active_requests = 0  # Track active requests
var max_concurrent_requests = 1  # Only allow 1 request at a time
```

### 2. **Queue-Aware Request Management**
```gdscript
func _wait_for_request_slot():
    while active_requests >= max_concurrent_requests:
        push_warning("Waiting for request slot (currently %d/%d active)" % [active_requests, max_concurrent_requests])
        await get_tree().create_timer(1.0).timeout
```

### 3. **Exponential Backoff for 429 Errors**
```gdscript
if response_code == 429:
    if attempt < max_retries - 1:
        var wait_time = (attempt + 1) * 5.0  # 5s, 10s, 15s
        push_warning("Queue full (429), waiting %.1f seconds before retry" % wait_time)
        await get_tree().create_timer(wait_time).timeout
```

### 4. **Reduced Game Demands**
- **NPCs per day**: Reduced from 5 to 3
- **Generation delays**: Increased to 2 seconds between NPC creations
- **Request spacing**: 3 seconds minimum between any AI requests

### 5. **Better Error Reporting**
All errors now use `push_error()` and `push_warning()` for easier debugging:
```gdscript
push_error("ERROR 429: Server queue full! Response: %s" % body_text)
push_warning("Rate limiting: waiting %.1f seconds" % wait_time)
```

## How It Works Now

### Request Flow:
1. **Check Slot Availability**: Wait if already 1 request active
2. **Mark Request Active**: Increment active_requests counter
3. **Rate Limiting**: Wait 3 seconds since last request
4. **Make Request**: Send to AI server
5. **Handle 429**: If queue full, exponential backoff (5s, 10s, 15s)
6. **Mark Complete**: Decrement active_requests counter

### Game Flow:
1. **NPC Generation**: One at a time with 2-second delays
2. **Consequence Analysis**: Only when NPC presents request
3. **Fallback Systems**: Basic consequences if AI fails

## Configuration Options

### For Different Patron Tiers:

**Current (Queue Limit 2):**
```gdscript
max_concurrent_requests = 1
min_request_interval = 3.0
npcs_per_day = 3
```

**If You Upgrade Patron Tier:**
```gdscript
max_concurrent_requests = 2  # Can handle more concurrent requests
min_request_interval = 2.0   # Faster requests
npcs_per_day = 5            # More NPCs per day
```

## Testing Strategy

### 1. **Monitor Queue Status**
Watch for these messages:
- `"Waiting for request slot"` - Good, preventing queue overflow
- `"Queue full (429), waiting X seconds"` - Expected, will retry
- `"Failed after 3 attempts"` - Problem, may need longer delays

### 2. **Adjust If Needed**
If still getting 429 errors:
```gdscript
# Make it even more conservative
min_request_interval = 5.0  # 5 seconds between requests
npcs_per_day = 2           # Only 2 NPCs per day
```

### 3. **Success Indicators**
- No 429 errors in console
- Smooth NPC generation with delays
- AI responses working when queue available

## Long-term Solutions

### 1. **Upgrade Patron Tier**
- Increases queue limit beyond 2
- Allows more concurrent requests
- Enables faster gameplay

### 2. **Optimize AI Usage**
- Cache common responses
- Use simpler prompts when possible
- Batch similar requests

### 3. **Fallback-First Design**
- Always have non-AI alternatives
- Use AI for enhancement, not core functionality
- Graceful degradation when AI unavailable

## Expected Behavior Now

1. **Slower but Stable**: Game will be slower but won't crash with 429 errors
2. **Clear Feedback**: You'll see exactly what's happening in the console
3. **Automatic Recovery**: Will retry failed requests with backoff
4. **Graceful Fallbacks**: Game continues even if AI is unavailable

The game should now work reliably within your server's queue limitations!

extends Node

# Simple test script to debug AI API calls
@onready var ai = Player2

func _ready():
	print("=== AI API TEST SCRIPT ===")
	await get_tree().create_timer(1.0).timeout  # Wait for Player2 to initialize
	
	if not ai.server_ok:
		print("ERROR: AI server is not available!")
		return
	
	print("AI server is ready. Starting tests...")
	await run_tests()

func run_tests():
	print("\n--- Test 1: Simple name generation ---")
	var name_result = await ai.create_chat_completion([
		{"role": "user", "content": "Generate a name for a merchant"}
	], "Generate a single medieval fantasy name for a merchant. Respond with only the name, nothing else.")
	
	print("Name result: '%s'" % name_result)
	
	print("\n--- Test 2: Simple conversation ---")
	var chat_result = await ai.create_chat_completion([
		{"role": "user", "content": "Hello, how are you?"}
	], "You are a helpful assistant.")
	
	print("Chat result: '%s'" % chat_result)
	
	print("\n--- Test 3: JSON response test ---")
	var json_result = await ai.create_chat_completion([
		{"role": "user", "content": "Analyze this decision"}
	], """Respond with a JSON object containing:
{
  "stat_changes": {
    "happiness": 10,
    "treasury": -20
  },
  "description": "A simple test response"
}

Respond ONLY with valid JSON.""")
	
	print("JSON result: '%s'" % json_result)
	
	# Try to parse the JSON
	var parsed = JSON.parse_string(json_result)
	if parsed != null:
		print("JSON parsing successful: %s" % str(parsed))
	else:
		print("JSON parsing failed")
	
	print("\n--- Test 4: Rate limiting test ---")
	print("Making 3 rapid requests to test rate limiting...")
	
	for i in range(3):
		print("Request %d..." % (i + 1))
		var rapid_result = await ai.create_chat_completion([
			{"role": "user", "content": "Say hello %d" % (i + 1)}
		], "You are a helpful assistant. Keep responses short.")
		print("Result %d: '%s'" % [i + 1, rapid_result])
	
	print("\n=== ALL TESTS COMPLETED ===")

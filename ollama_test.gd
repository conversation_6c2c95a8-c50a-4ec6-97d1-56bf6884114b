extends Node

# Test script for Ollama API integration
@onready var ollama = Ollama

func _ready():
	print("=== OLLAMA API TEST SCRIPT ===")
	await get_tree().create_timer(2.0).timeout  # Wait for Ollama to initialize
	
	if not ollama.server_ok:
		print("ERROR: Ollama server is not available!")
		print("Make sure Ollama is running and llama3.2:3b model is installed")
		print("Commands to run:")
		print("  ollama serve")
		print("  ollama pull llama3.2:3b")
		return
	
	print("Ollama server is ready. Starting tests...")
	await run_tests()

func run_tests():
	print("\n--- Test 1: Simple name generation ---")
	var name_result = await ollama.create_chat_completion([
		{"role": "user", "content": "Generate a single medieval fantasy name for a merchant"}
	], "You are a helpful assistant. Generate only the name, nothing else.")
	
	print("Name result: '%s'" % name_result)
	
	print("\n--- Test 2: Simple conversation ---")
	var chat_result = await ollama.create_chat_completion([
		{"role": "user", "content": "Hello! Tell me a short joke."}
	], "You are a friendly assistant who likes to tell jokes.")
	
	print("Chat result: '%s'" % chat_result)
	
	print("\n--- Test 3: JSON response test ---")
	var json_result = await ollama.create_chat_completion([
		{"role": "user", "content": "Create a simple JSON response"}
	], """You must respond with valid JSON in this exact format:
{
  "stat_changes": {
    "happiness": 10,
    "treasury": -20
  },
  "description": "A simple test response"
}

Respond ONLY with the JSON, no other text.""")
	
	print("JSON result: '%s'" % json_result)
	
	# Try to parse the JSON
	var parsed = JSON.parse_string(json_result)
	if parsed != null:
		print("JSON parsing successful: %s" % str(parsed))
	else:
		print("JSON parsing failed - this is normal for LLMs, they often add extra text")
	
	print("\n--- Test 4: Rate limiting test ---")
	print("Making 3 requests to test rate limiting...")
	
	for i in range(3):
		print("Request %d..." % (i + 1))
		var rapid_result = await ollama.create_chat_completion([
			{"role": "user", "content": "Say hello number %d" % (i + 1)}
		], "You are a helpful assistant. Keep responses very short.")
		print("Result %d: '%s'" % [i + 1, rapid_result])
	
	print("\n--- Test 5: Context and history test ---")
	var context_result = await ollama.create_chat_completion([
		{"role": "user", "content": "My name is John"},
		{"role": "assistant", "content": "Hello John! Nice to meet you."},
		{"role": "user", "content": "What is my name?"}
	], "You are a helpful assistant with good memory.")
	
	print("Context result: '%s'" % context_result)
	
	print("\n=== ALL OLLAMA TESTS COMPLETED ===")
	print("If all tests worked, Ollama is ready for the game!")
